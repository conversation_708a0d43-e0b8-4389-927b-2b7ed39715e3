<?php
require_once 'config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Database Structure Check - profil_sekolah</h2>";

// Check if table exists
$query = "SHOW TABLES LIKE 'profil_sekolah'";
$stmt = $conn->prepare($query);
$stmt->execute();

if ($stmt->rowCount() > 0) {
    echo "<h3>Table Structure:</h3>";
    
    // Get table structure
    $query = "DESCRIBE profil_sekolah";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>Sample Data:</h3>";
    
    // Get sample data
    $query = "SELECT * FROM profil_sekolah LIMIT 1";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    
    if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        
        foreach ($row as $field => $value) {
            echo "<tr>";
            echo "<td><strong>" . $field . "</strong></td>";
            echo "<td>" . ($value ?: '<em>NULL/Empty</em>') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p><strong>No data found in profil_sekolah table</strong></p>";
    }
    
} else {
    echo "<p><strong>Table 'profil_sekolah' does not exist!</strong></p>";
}
?>
